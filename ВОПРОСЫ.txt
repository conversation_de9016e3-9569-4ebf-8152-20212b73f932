РАЗДЕЛ 1 – БЕСПРОВОДНЫЕ ВЫЧИСЛИТЕЛЬНЫЕ СЕТИ
1. Роль вычислительных сетей в развитии технологий беспроводной связи. Отличия
беспроводных вычислительных сетей от проводных вычислительных сетей (с точки
зрения проектирования, эксплуатации и обеспечения безопасности).
2. Роль телефонных сетей в развитии технологий беспроводной связи. Отличия
беспроводных вычислительных сетей от беспроводных телефонных сетей (с точки
зрения проектирования, эксплуатации и обеспечения безопасности).
3. Классификация беспроводных компьютерных сетей по функциональному
назначению и территориальному охвату (с примерами технологий и протоколов).
4. Физический уровень в беспроводных вычислительных сетях. Решаемые на
физическом уровне задачи (мультиплексирование, расширение спектра, модуляция,
формирование лучей).
5. Канальный уровень в беспроводных вычислительных сетях. Решаемые на канальном
уровне задачи (адресация в локальной сети, управление средой передачи,
управление каналом, защита канала и данных).
6. Частотные диапазоны беспроводных вычислительных сетей. ISM-диапазон.
Частотное разделение каналов в WLAN. Влияние частотного диапазона и ширины
канала на эксплуатационные характеристики сети (пропускную способность,
дальнодействие связи).
7. Модуляция в беспроводных сетях связи (BPSK, QPSK, QAM). Преимущества,
недостатки, влияние на характеристики канала. Избыточность и скорость
кодирования.
8. Преимущества расширения спектра. Методы расширения спектра в беспроводных
сетях связи (FHSS, DSSS, CSS). Технология OFDM.
9. Характеристики антенного оборудования. Диаграммы направленности.
Фазированные решётки. Технология формирования луча (beamforming). Технология
пространственного кодирования (MIMO).
10. Проблема управления каналом в беспроводных сетях. Проблема скрытого
(экспонированного) узла. Разрешительный механизм отправки сообщений
(RTS/CTS).
11. Проблема управления потоком данных в беспроводных каналах. Технология
CSMA/CA. Межкадровые интервалы. Физический и виртуальный контроль
несущей.
12. Проблема управления доступом к среде в беспроводных сетях. Централизованное
(PCF), распределённое (DCF) и смешанное (HCF) управление доступом.
13. Подуровни локальных сетей в группе стандартов IEEE 802. Функции подуровней.
14. Беспроводные сети WPAN. Функциональное назначение. Топология и устройства.
Типичные требования по безопасности. Протоколы IEEE 802.15, IEEE 802.15.4.
15. Беспроводные сети WMAN. Функциональное назначение. Топология и устройства.
Типичные требования по безопасности. Протоколы IEEE 802.16, LoRaWAN.
16. Оборудование WLAN сетей (адаптеры, точки доступы, повторители, мосты):
назначение, применение, характеристики.
17. Структура MAC-кадров в WLAN. Заголовок кадра. Назначение адресных полей.
Назначение сервисных полей (QoS control, HT control).
18. Управление качеством сервиса (QoS): назначение, механизмы. Виды трафика.
19. Типы (подтипы) кадров в WLAN. Их назначение и функции.
20. Механизмы взаимного обнаружения устройств в WLAN. Скрытые сети.
21. Механизмы установления соединения в WLAN. Аутентификация и ассоциация.
Механизм быстрой передачи обслуживания (Fast Transition).
22. Понятия базового (BSS), расширенного (ESS) и независимого (ISS) наборов услуг в
WLAN. Топология, функции и особенности применения. Технология Wi-Fi Direct.
РАЗДЕЛ 2 – БЕЗОПАСНОСТЬ БЕСПРОВОДНЫХ СЕТЕЙ
1. Документы, определяющие технологию WLAN и границы их применения.
Документы IEEE. Документы Wi-Fi Alliance. Спецификация 802.11.
2. Архитектура безопасности в WLAN. Механизмы безопасности в RSN и pre-RSN
сетях.
3. Иерархия ключей в RSN-сетях (долговременные, сессионные, сеансовые) и их
назначение. Обобщённая процедура аутентификация и установления соединения в
RSN-сетях.
4. Протокол безопасности WEP. Принципы, заложенные при разработке. Защита
конфиденциальности и целостности.
5. Аутентификация в WEP. Виды аутентификации: OSA и SKA. Рукопожатие WEP.
6. Уязвимости WEP. Практические атаки на WEP: FMS/KoreK, PTW, KoreK ChopChop.
7. Протокол безопасности WPA. Принципы, заложенные при разработке. Обзор
изменений в механизмах безопасности.
8. Криптографический протокол TKIP. Используемые криптографические примитивы.
Схема их применения. Преимущества по сравнению с WEP.
9. Алгоритм вычисления дайджеста MICHAEL. Принцип построения. Известные
уязвимости. Преимущества по сравнению с ICV в WEP.
10. Протокол безопасности WPA2. Принципы, заложенные при разработке. Обзор
изменений в механизмах безопасности.
11. Криптографический протокол CCMP. Используемые криптографические
примитивы. Схема их применения. Преимущества по сравнению с TKIP.
12. Аутентификация в WPA/WPA2. Четырёхэтапное рукопожатие (схема, функция).
Словарная атака на WPA-PSK.
13. Применение EAP в WLAN для распределения сессионных ключей. Аутентификация
в WPA-Enterprise. Протоколы EAPoL, RADIUS.
14. Требования по безопасности к методам аутентификации RFC3748. Методы
аутентификации EAP-MD5, EAP-TLS, EAP-PEAP.
15. Спецификация WPS. Принцип аутентификации устройств. Варианты применения:
PIN, PBC, со сторонним каналом. Безопасные сценарии применения.
16. Рукопожатие в WPS. Проблемы и уязвимости. Атака Reaver. Атака Pixie Dust.
17. Протокол безопасности WPA3. Принципы, заложенные при разработке. Обзор
изменений в механизмах безопасности.
18. Криптографический протокол GCMP. Используемые криптографические
примитивы. Схема их применения. Преимущества по сравнению с CCMP.
19. Защищённые служебные кадры (PMF). Подтипы защищаемых кадров. Устойчивость
к атакам типа «отказ в обслуживании».
20. Распределение PMK на основе общего пароля. Рукопожатие SAE (схема, функция).
Независимость сессий и устойчивость к словарным атакам.
21. Спецификация Wi-Fi Easy Connect (протокол DPP). Модульная структура.
Преимущества по сравнению с WPS.
22. Утилиты aircrack-ng, Reaver, mdk-4. Возможности и функции. Реализованные атаки.
23. Средства тестирования WLAN на проникновение. Система обнаружения вторжений
в WLAN. Утилиты Kismet, WiFite, Airgeddon, AtEar.